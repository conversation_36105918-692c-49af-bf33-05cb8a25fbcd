import { Logger } from '@nestjs/common';
import { Anthropic } from '@llamaindex/anthropic';
import { BedrockLlmService } from '../../../utils/bedrockLlm';
import { PageMarkdown, PageAnalysisResult } from '../types/invoice-splitter.types';

export class InvoiceSplitterAgent {
  private readonly logger = new Logger(InvoiceSplitterAgent.name);
  private llm: any;

  constructor(provider: 'bedrock' | 'anthropic' = 'bedrock') {
    this.logger.log(`Initializing InvoiceSplitterAgent with provider: ${provider}`);

    if (provider === 'bedrock') {
      this.llm = new BedrockLlmService();
    } else {
      this.llm = new Anthropic({
        apiKey: process.env.ANTHROPIC_KEY,
        model: 'claude-3-5-sonnet-20241022',
      });
    }
  }

  async analyzePages(pageMarkdowns: PageMarkdown[]): Promise<PageAnalysisResult> {
    try {
      this.logger.log(`Starting invoice analysis for ${pageMarkdowns.length} pages`);

      const prompt = this.buildPageAnalysisPrompt(pageMarkdowns);

      const response = await this.llm.chat({
        messages: [
          {
            role: 'system',
            content: `You are an expert document analyst specializing in invoice detection and page boundary identification. Your task is to analyze document pages and determine which pages belong to which invoice. You have deep expertise in understanding invoice structures, document formatting, and multi-page invoice patterns.

Your analysis should be precise and methodical:
1. Look for clear invoice boundaries like new invoice numbers, different vendors, separate totals
2. Understand that invoices can span multiple pages
3. Identify continuation pages that belong to previous invoices
4. Consider document flow and logical groupings
5. Provide high confidence scores for clear separations and lower for ambiguous cases

Always respond with valid JSON only - no explanations or markdown formatting.`,
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
      });

      // Parse the JSON response
      let rawContent: string;
      if (typeof response.message.content === 'string') {
        rawContent = response.message.content;
      } else if (Array.isArray(response.message.content) && response.message.content.length > 0) {
        // Anthropic format: [{"type":"text","text":"actual JSON content"}]
        const firstItem = response.message.content[0];
        if (firstItem && firstItem.type === 'text' && firstItem.text) {
          rawContent = firstItem.text;
        } else {
          rawContent = JSON.stringify(response.message.content);
        }
      } else if (response.message.content && typeof response.message.content === 'object') {
        rawContent = JSON.stringify(response.message.content);
      } else {
        rawContent = String(response.message.content || '');
      }

      this.logger.debug(`Raw response: ${rawContent.substring(0, 300)}...`);

      const parsedResult = this.parseJsonResponse(rawContent);
      
      // Validate the structure
      if (!parsedResult.totalInvoices || !Array.isArray(parsedResult.pageGroups)) {
        throw new Error('Invalid response structure from LLM');
      }

      this.logger.log(`Invoice analysis completed: ${parsedResult.totalInvoices} invoices detected`);

      return parsedResult as PageAnalysisResult;
    } catch (error) {
      this.logger.error('Invoice analysis failed:', error);
      
      // Return fallback: treat all pages as single invoice
      return {
        totalInvoices: 1,
        pageGroups: [
          {
            invoiceNumber: 1,
            pages: pageMarkdowns.map(p => p.pageNumber),
            confidence: 0.3,
            reasoning: `Analysis failed (${error.message}), treating as single invoice`
          }
        ]
      };
    }
  }

  private buildPageAnalysisPrompt(pages: PageMarkdown[]): string {
    const pagesContent = pages.map(page => 
      `=== PAGE ${page.pageNumber} ===\n${page.content.substring(0, 2000)}${page.content.length > 2000 ? '...' : ''}\n`
    ).join('\n');

    return `Analyze these PDF pages to identify individual receipts/invoices. Each page could potentially be a separate receipt unless proven otherwise.

DOCUMENT PAGES:
${pagesContent}

ANALYSIS APPROACH:
1. ASSUME each page is a separate receipt/invoice by default
2. Only group pages together if they clearly belong to the same transaction
3. Focus on TRANSACTION BOUNDARIES rather than vendor similarity
4. Look for COMPLETE TRANSACTION CYCLES on individual pages

INDIVIDUAL RECEIPT INDICATORS (Each suggests a separate receipt):
- Complete transaction with total amount
- Separate date/time stamps
- Different receipt/invoice numbers
- Different order/confirmation numbers
- Separate payment method information
- Different transaction IDs or reference numbers
- Complete item lists with subtotals and taxes
- Different store locations or addresses (even same brand)
- Separate "Thank you" or transaction completion messages
- Different customer information
- Standalone QR codes or barcodes

GROUPING RULES (Only group when ALL criteria match):
- SAME exact invoice/receipt number AND
- SAME exact transaction date/time AND
- SAME exact total amount AND
- Clear continuation indicators (like "Page 2 of 3") AND
- Incomplete transaction on first page (no total/completion)

SEPARATION RULES (Always separate when ANY of these exist):
- Different total amounts (even same vendor)
- Different dates or times (even same day)
- Different receipt/invoice numbers
- Different payment methods or card numbers
- Complete transaction cycle on each page
- Different store numbers or locations
- Different customer names or details
- Separate tax calculations

SPECIAL CASES TO WATCH FOR:
- Digital receipt compilations (multiple complete receipts from same vendor)
- Expense report collections (employee submitting multiple receipts)
- Receipt scanning app outputs (various receipts with app branding)
- Multi-location same brand receipts (different store numbers/addresses)
- Subscription billing (multiple months from same provider)
- E-commerce platform receipts (same platform, different sellers)

CONFIDENCE SCORING:
- 0.9-1.0: Clear individual receipts with complete transaction cycles
- 0.7-0.8: Likely separate receipts with minor ambiguities
- 0.5-0.6: Possible separate receipts, some shared elements
- 0.3-0.4: Unclear boundaries, lean toward separation
- 0.0-0.2: Strong evidence for grouping (true multi-page invoice)

RESPONSE FORMAT (valid JSON only):
{
  "totalInvoices": 3,
  "pageGroups": [
    {
      "invoiceNumber": 1,
      "pages": [1],
      "confidence": 0.95,
      "reasoning": "Page 1: Complete Starbucks receipt #12345, total $4.50, 2024-01-15 09:30"
    },
    {
      "invoiceNumber": 2,
      "pages": [2],
      "confidence": 0.92,
      "reasoning": "Page 2: Complete Starbucks receipt #12346, total $3.25, 2024-01-15 14:20 - different time/amount"
    },
    {
      "invoiceNumber": 3,
      "pages": [3, 4],
      "confidence": 0.88,
      "reasoning": "Pages 3-4: Multi-page invoice #INV-2024-001, same total $150.00, page continuation indicators"
    }
  ]
}

CRITICAL: Respond with ONLY the JSON object. No explanations, markdown formatting, or additional text.`;
  }

  private parseJsonResponse(content: string): any {
    try {
      // Remove markdown code blocks if present
      const cleanContent = content
        .replace(/```json\s*/g, '')
        .replace(/```\s*/g, '')
        .trim();

      return JSON.parse(cleanContent);
    } catch (error) {
      this.logger.error('Failed to parse JSON response:', error);
      throw new Error(`Invalid JSON response: ${error.message}`);
    }
  }
}
