{"receiptStandards": [{"description": "Clear and readable receipts must be submitted", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Atlas, Global People, goGlobal, Parakar", "mandatoryOptional": "Mandatory", "rule": "Clear and readable receipts and invoices must be submitted with expenses"}, {"description": "Must be actual tax receipts or invoices", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Atlas, Global People, goGlobal, Parakar", "mandatoryOptional": "Mandatory", "rule": "Documents must be actual tax receipts or invoices; booking confirmations will not suffice"}, {"description": "Online copies are sufficient", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Atlas, Global People, goGlobal, Parakar", "mandatoryOptional": "Optional", "rule": "Online copies are sufficient, a hard copy is not required"}, {"description": "Local Employer company name must appear on invoices", "travelNonTravelBoth": "Both", "expenseType": "All expenses (except flights/hotels)", "icpName": "Global People", "mandatoryOptional": "Mandatory", "rule": "Invoices must show \"Global People DE GmbH\" not the worker's name"}, {"description": "Local Employer address must appear on invoices", "travelNonTravelBoth": "Both", "expenseType": "All expenses (except flights/hotels)", "icpName": "Global People", "mandatoryOptional": "Mandatory", "rule": "Invoices must show \"Taunusanlage 8, 60329 Frankfurt, Germany\""}, {"description": "Local Employer VAT ID must appear on invoices", "travelNonTravelBoth": "Both", "expenseType": "All expenses (except flights/hotels)", "icpName": "Global People", "mandatoryOptional": "Mandatory", "rule": "Invoices must show \"VAT ID: DE356366640\""}, {"description": "Worker or company name can appear on invoices", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Atlas", "mandatoryOptional": "Optional", "rule": "The worker or the company's name can appear on the invoice or supporting documents"}, {"description": "Local Employer name required for tax-free reimbursement", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Atlas, Global People, goGlobal, Parakar", "mandatoryOptional": "Mandatory", "rule": "Under certain Local employers, if invoices are not issued in the Local Employers name, then tax free reimbursement is not possible"}, {"description": "Local Employer company name must appear on invoices", "travelNonTravelBoth": "Both", "expenseType": "All expenses (except flights/hotels)", "icpName": "goGlobal", "mandatoryOptional": "Mandatory", "rule": "Invoices must show \"GoGlobal Germany GmbH\" not the worker's name"}, {"description": "Local Employer address must appear on invoices", "travelNonTravelBoth": "Both", "expenseType": "All expenses (except flights/hotels)", "icpName": "goGlobal", "mandatoryOptional": "Mandatory", "rule": "Invoices must show \"Prielmayerstrasse 3, 80335 Munich, Germany\""}, {"description": "Local Employer company name must appear on invoices", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "<PERSON><PERSON>", "mandatoryOptional": "Mandatory", "rule": "Invoices must show \"Parakar Germany GmbH\" not the worker's name"}, {"description": "Local Employer address must appear on invoices", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "<PERSON><PERSON>", "mandatoryOptional": "Mandatory", "rule": "Invoices must show \"Friesenpl. 4, 50672 Koln, Germany\""}, {"description": "Worker name must appear on hotel/flight invoices", "travelNonTravelBoth": "Travel", "expenseType": "Hotel, Flight", "icpName": "Global People", "mandatoryOptional": "Mandatory", "rule": "Hotel/flights invoices should indicate the name of the worker not the company"}, {"description": "Invoices over 150 EUR must include employer details", "travelNonTravelBoth": "Both", "expenseType": "All expenses over 150 EUR", "icpName": "Global People", "mandatoryOptional": "Mandatory", "rule": "If invoices are over €150, are required to note the employer's name, address and VAT amount"}, {"description": "Mobile phone invoices must include company name", "travelNonTravelBoth": "Non-Travel", "expenseType": "Mobile phone", "icpName": "<PERSON><PERSON>", "mandatoryOptional": "Mandatory", "rule": "Any mobile phone invoice that does not include \"Parakar Germany GmbH\" (at least in c/o) cannot be reimbursed"}, {"description": "Home internet invoices must include company name", "travelNonTravelBoth": "Non-Travel", "expenseType": "Home internet", "icpName": "<PERSON><PERSON>", "mandatoryOptional": "Mandatory", "rule": "Any home internet invoice that does not include \"Parakar Germany GmbH\" (at least in c/o) cannot be reimbursed"}, {"description": "Invoices over 450 EUR must include additional information", "travelNonTravelBoth": "Non-Travel", "expenseType": "All expenses over 450 EUR", "icpName": "<PERSON><PERSON>", "mandatoryOptional": "Mandatory", "rule": "Invoices exceeding gross amount of EUR 450 must contain: worker name and address, tax/VAT ID of restaurant, invoice serial number, invoice date and meal date, net amount, applicable tax rate and VAT"}], "compliancePoliciesGrossUpRelated": [{"travelNonTravelBoth": "Non-Travel", "expenseType": "Business expenses (general)", "icpName": "Atlas, Global People, goGlobal, Parakar", "grossUp": "Yes", "grossUpRule": "Business expenses related to workers completing their job will usually be tax exempt. Only purely business related elements will be tax free, anything additional will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Office equipment", "icpName": "Atlas", "grossUp": "No", "grossUpRule": "Office equipment (business use) is tax exempt"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Training (job-related)", "icpName": "Atlas", "grossUp": "No", "grossUpRule": "Training (job-related) is tax exempt"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Phone/internet", "icpName": "Atlas", "grossUp": "Yes", "grossUpRule": "Phone/internet up to €20/month max is tax exempt. Amounts above €20/month will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Home office", "icpName": "Atlas", "grossUp": "Yes", "grossUpRule": "Home office up to €6/day, max €1,260/year is tax exempt. Amounts above these limits will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Wellness benefits", "icpName": "Atlas", "grossUp": "Yes", "grossUpRule": "Wellness benefits up to max €600/year are tax exempt. Amounts above €600/year will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "IT equipment", "icpName": "Global People", "grossUp": "No", "grossUpRule": "IT equipment that is considered as company property is tax exempt"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Office supplies", "icpName": "Global People", "grossUp": "No", "grossUpRule": "Office supplies that are relevant and reasonable in amount are tax exempt"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Meals (outside business travel)", "icpName": "Global People", "grossUp": "Yes", "grossUpRule": "Meals (outside of the business travel report) are not tax exempt and will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Fuel", "icpName": "Global People", "grossUp": "Yes", "grossUpRule": "Fuel is not tax exempt and will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Phone Bill", "icpName": "Global People", "grossUp": "Yes", "grossUpRule": "Phone Bill is not tax exempt and will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Transportation to workplace", "icpName": "Global People", "grossUp": "Yes", "grossUpRule": "Transportation to the workplace is not tax exempt and will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Office groceries", "icpName": "Global People", "grossUp": "Yes", "grossUpRule": "Office groceries are not tax exempt and will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Non-essential items", "icpName": "Global People", "grossUp": "Yes", "grossUpRule": "Other items which are not essential to carry out work activity are not tax exempt and will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Entertainment", "icpName": "Global People", "grossUp": "Yes", "grossUpRule": "Entertainment expenses are tax exempt only when offered to a third party and invoices note Global People's name, address and VAT amount. Entertainment expenses solely for employees of same company will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Telephone costs", "icpName": "goGlobal, Parakar", "grossUp": "Yes", "grossUpRule": "Telephone costs up to 20% of invoiced amount, max EUR 20 per month is tax exempt. Amounts above these limits will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Mobile phone usage", "icpName": "goGlobal, Parakar", "grossUp": "Yes", "grossUpRule": "Mobile phone usage is tax exempt only if worker uses separate phone for personal use with proof. Without proof will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Internet costs", "icpName": "goGlobal", "grossUp": "Yes", "grossUpRule": "Internet costs up to 25% of invoice amount is tax exempt. Amounts above 25% will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Personal meals", "icpName": "<PERSON><PERSON>", "grossUp": "Yes", "grossUpRule": "Personal meals cannot be reimbursed tax-free and will be grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Average expenses on business behalf", "icpName": "<PERSON><PERSON>", "grossUp": "No", "grossUpRule": "Based on three month record an average percentage rate is determined on how much private cell phone is used for business purposes - this is tax exempt"}, {"travelNonTravelBoth": "Travel", "expenseType": "Mileage reimbursement", "icpName": "Atlas", "grossUp": "No", "grossUpRule": "Mileage reimbursement at €0.30 per km (standard tax-free rate) is tax exempt"}, {"travelNonTravelBoth": "Travel", "expenseType": "Mileage reimbursement", "icpName": "Global People", "grossUp": "Yes", "grossUpRule": "KM reimbursement will be tax exempt but fuel will be grossed up"}, {"travelNonTravelBoth": "Travel", "expenseType": "Domestic business travel per diem", "icpName": "Atlas, Global People, goGlobal, Parakar", "grossUp": "Yes", "grossUpRule": "Per diem within legal rates (€14/day over 8h, €28/day for 24h, €14 arrival/departure days) is tax exempt. Amounts above legal rates will be grossed up"}, {"travelNonTravelBoth": "Travel", "expenseType": "International business travel per diem", "icpName": "Atlas, Global People, goGlobal, Parakar", "grossUp": "Yes", "grossUpRule": "Per diem within government rates per country is tax exempt for up to three months of continuous business travel in a given location. Per diems above government rates or after three months will be grossed up. Long stays (more than three days in a week) extending beyond three months are taxable"}, {"travelNonTravelBoth": "Travel", "expenseType": "Business travel expenses", "icpName": "Global People", "grossUp": "No", "grossUpRule": "As a general rule, reimbursements related to travel expenses are tax exempt"}], "compliancePoliciesAdditionalInfoRelated": [{"travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Atlas, Global People, goGlobal, Parakar", "additionalInfoRequired": "Yes", "additionalInfoRule": "Receipts and invoices must be submitted with expenses"}, {"travelNonTravelBoth": "Both", "expenseType": "Currency and exchange rate", "icpName": "Global People", "additionalInfoRequired": "Yes", "additionalInfoRule": "Receipts should be submitted with the same currency and clear exchange rate"}, {"travelNonTravelBoth": "Both", "expenseType": "Expense submission method", "icpName": "Atlas, Global People, goGlobal, Parakar", "additionalInfoRequired": "Yes", "additionalInfoRule": "Expenses can be submitted through Expensify or manual expense report (to be agreed between worker and manager)"}, {"travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global People", "additionalInfoRequired": "Yes", "additionalInfoRule": "Detailed context must be provided so Global People can identify tax free expenses"}, {"travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global People", "additionalInfoRequired": "Yes", "additionalInfoRule": "One consolidated report per employee must be submitted"}, {"travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global People", "additionalInfoRequired": "Yes", "additionalInfoRule": "Expense details must include: type of expense, context, copy of the invoice"}, {"travelNonTravelBoth": "Both", "expenseType": "Training expenses", "icpName": "Global People", "additionalInfoRequired": "Yes", "additionalInfoRule": "Training expenses require the approval of the direct manager"}, {"travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "<PERSON><PERSON>", "additionalInfoRequired": "Yes", "additionalInfoRule": "Original invoices, although stored digitally, need to be kept for 10 years"}, {"travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "<PERSON><PERSON>", "additionalInfoRequired": "Yes", "additionalInfoRule": "Description of the reason for the expense should be as precise and detailed as possible"}, {"travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "<PERSON><PERSON>", "additionalInfoRequired": "Yes", "additionalInfoRule": "Parakar only accepts expenses that are properly documented by means of invoices or receipts from the provider, that specify the service provided"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Mobile phone usage proof", "icpName": "goGlobal, Parakar", "additionalInfoRequired": "Yes", "additionalInfoRule": "Mobile phone usage requires proof that worker uses separate phone for personal use"}, {"travelNonTravelBoth": "Travel", "expenseType": "All travel expenses", "icpName": "Global People", "additionalInfoRequired": "Yes", "additionalInfoRule": "All travel related expenses must be reported using Travel Expense Report.xlsx template"}, {"travelNonTravelBoth": "Travel", "expenseType": "Business travel", "icpName": "Atlas, Global People, goGlobal, Parakar", "additionalInfoRequired": "Yes", "additionalInfoRule": "Business travel expenses must be submitted using the travel report template"}, {"travelNonTravelBoth": "Travel", "expenseType": "Business travel", "icpName": "Global People", "additionalInfoRequired": "Yes", "additionalInfoRule": "Business trip - submit a separate report for each trip"}, {"travelNonTravelBoth": "Travel", "expenseType": "Business travel", "icpName": "Global People", "additionalInfoRequired": "Yes", "additionalInfoRule": "Supporting documents required to ensure flight reimbursement is processed as net: missing payment receipt/invoice as well as ticket copy"}, {"travelNonTravelBoth": "Travel", "expenseType": "International travel", "icpName": "goGlobal", "additionalInfoRequired": "Yes", "additionalInfoRule": "An A1 certificate is required when travelling"}, {"travelNonTravelBoth": "Travel", "expenseType": "Domestic business travel per diem", "icpName": "Atlas, Global People", "additionalInfoRequired": "Yes", "additionalInfoRule": "Per diem method cannot be mixed with actual expenses. You will need to agree with the worker on one method per business trip"}, {"travelNonTravelBoth": "Travel", "expenseType": "International business travel per diem", "icpName": "Atlas, Global People, goGlobal, Parakar", "additionalInfoRequired": "Yes", "additionalInfoRule": "Per diem method cannot be mixed with actual expenses. You will need to agree with the worker on one method per business trip"}, {"travelNonTravelBoth": "Travel", "expenseType": "Domestic business travel per diem with provided meals", "icpName": "Atlas, Global People, goGlobal, Parakar", "additionalInfoRequired": "Yes", "additionalInfoRule": "If meals have been pre-arranged the meal allowance should be reduced by 20 to 40% depending on which meals were provided"}, {"travelNonTravelBoth": "Travel", "expenseType": "International business travel per diem with provided meals", "icpName": "Atlas, Global People, goGlobal, Parakar", "additionalInfoRequired": "Yes", "additionalInfoRule": "When meals are offered at no cost, the daily meal allowance will be reduced by 20 to 40% depending on which meals were provided"}, {"travelNonTravelBoth": "Travel", "expenseType": "Domestic business travel per diem with all meals provided", "icpName": "goGlobal, Parakar", "additionalInfoRequired": "Yes", "additionalInfoRule": "For domestic business trips, the total allowance per day is reduced to zero when all meals (breakfast, lunch and dinner) are provided"}, {"travelNonTravelBoth": "Travel", "expenseType": "Per diem", "icpName": "goGlobal, Parakar", "additionalInfoRequired": "Yes", "additionalInfoRule": "No receipts or invoices need to be provided when using the per diem method"}, {"travelNonTravelBoth": "Travel", "expenseType": "Mileage", "icpName": "Atlas", "additionalInfoRequired": "Yes", "additionalInfoRule": "Mileage reimbursement requires providing a Fahrtenbuch (mileage logbook), which must include date, route, purpose, and odometer readings"}, {"travelNonTravelBoth": "Travel", "expenseType": "Mileage", "icpName": "Global People", "additionalInfoRequired": "Yes", "additionalInfoRule": "Worker will need to share a map with the relevant route (google maps is sufficient) and complete the travel report where applicable"}, {"travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Atlas, Global People, goGlobal, Parakar", "additionalInfoRequired": "Yes", "additionalInfoRule": "Expense reimbursements will be either processed along the salary and reflected in worker's pay slip or paid out separately, depending on local employer policy"}]}