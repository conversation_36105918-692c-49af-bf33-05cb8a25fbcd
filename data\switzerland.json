{"receiptStandards": [{"description": "Receipt/Invoice document type", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "Documents must be actual tax receipts or invoices; booking confirmations will not be sufficient"}, {"description": "Receipt format and quality", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "Online copies of invoices and receipts are sufficient, a hard copy is not required"}, {"description": "Receipts and invoices submission", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "Receipts and invoices must be submitted alongside expense items"}, {"description": "Local currency reporting", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "The receipts/invoices should be reported in the local currency"}, {"description": "Company name on invoice", "travelNonTravelBoth": "Non-Travel", "expenseType": "IT equipment, Office supplies, Professional services, Training", "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "The Local Employer's name and details should appear on invoices: Global PPL CH GmbH"}, {"description": "Company address on invoice", "travelNonTravelBoth": "Non-Travel", "expenseType": "IT equipment, Office supplies, Professional services, Training", "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "Invoice must include address: Freigutstrasse 2 8002 Zürich, Switzerland"}, {"description": "Company registration number on invoice", "travelNonTravelBoth": "Non-Travel", "expenseType": "IT equipment, Office supplies, Professional services, Training", "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "Invoice must include CHE-295.369.918"}, {"description": "Worker name on travel documents", "travelNonTravelBoth": "Travel", "expenseType": "Flight, Hotel", "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "Exception when it is not possible to use the Local Employer's name, e.g. on flight bookings. The workers name should then be used instead"}, {"description": "Small business expenses limit", "travelNonTravelBoth": "Non-Travel", "expenseType": "Small business expenses", "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "Maximum of CHF 20 for smaller business expenses that are tax-free"}, {"description": "Mileage receipt exemption", "travelNonTravelBoth": "Travel", "expenseType": "Mileage", "icpName": "Global PPL CH GmbH", "mandatoryOptional": "Mandatory", "rule": "No receipts are required since this expense is based on usage rather than bills/invoices/receipt/logbook"}], "compliancePoliciesGrossUpRelated": [{"travelNonTravelBoth": "Both", "expenseType": "All approved business expenses", "icpName": "Global PPL CH GmbH", "grossUp": "Yes", "grossUpRule": "All approved expenses will be paid as NET to the employee and grossed up if they are not tax-free"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Business expenses (non-travel)", "icpName": "Global PPL CH GmbH", "grossUp": "No", "grossUpRule": "Business expenses related to workers completing their job are usually tax exempt - no gross up required"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Business expenses with personal elements", "icpName": "Global PPL CH GmbH", "grossUp": "Yes", "grossUpRule": "Only purely business related elements will be tax free, anything additional will be subject to tax and grossed up"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Small business expenses up to CHF 20", "icpName": "Global PPL CH GmbH", "grossUp": "No", "grossUpRule": "Smaller business expenses up to maximum CHF 20 are tax-free - no gross up required"}, {"travelNonTravelBoth": "Travel", "expenseType": "Mileage at official rates", "icpName": "Global PPL CH GmbH", "grossUp": "No", "grossUpRule": "Mileage reimbursed at official rates is tax exempt - no gross up required"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Training and development", "icpName": "Global PPL CH GmbH", "grossUp": "No", "grossUpRule": "Training expenses are tax exempt - no gross up required"}, {"travelNonTravelBoth": "Travel", "expenseType": "Accommodation/Hotels", "icpName": "Global PPL CH GmbH", "grossUp": "No", "grossUpRule": "Hotel expenses are tax exempt when for business travel - no gross up required"}, {"travelNonTravelBoth": "Travel", "expenseType": "Transportation", "icpName": "Global PPL CH GmbH", "grossUp": "No", "grossUpRule": "Transportation expenses for business travel are tax exempt - no gross up required"}], "compliancePoliciesAdditionalInfoRelated": [{"travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global PPL CH GmbH", "additionalInfoRequired": "Yes", "additionalInfoRule": "Workers should keep all their expenses in a simple report of their choice"}, {"travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global PPL CH GmbH", "additionalInfoRequired": "Yes", "additionalInfoRule": "Any personal information not required for reimbursement purposes should be removed before it is submitted"}, {"travelNonTravelBoth": "Both", "expenseType": "Foreign currency expenses", "icpName": "Global PPL CH GmbH", "additionalInfoRequired": "Yes", "additionalInfoRule": "The worker will need to add the FX rate they have used to calculate the expense"}, {"travelNonTravelBoth": "Travel", "expenseType": "Business trips", "icpName": "Global PPL CH GmbH", "additionalInfoRequired": "Yes", "additionalInfoRule": "Workers should submit a separate report per each business trip"}, {"travelNonTravelBoth": "Travel", "expenseType": "Mileage claims", "icpName": "Global PPL CH GmbH", "additionalInfoRequired": "Yes", "additionalInfoRule": "A logbook is required for each used car. The worker will need to share a map with the relevant route (google maps is sufficient) including route, car details and destination"}, {"travelNonTravelBoth": "Travel", "expenseType": "Multiple vehicle usage", "icpName": "Global PPL CH GmbH", "additionalInfoRequired": "Yes", "additionalInfoRule": "If a worker uses more than one vehicle in a year the mileage will all be calculated based on a combined mileage total"}, {"travelNonTravelBoth": "Travel", "expenseType": "Per diem method selection", "icpName": "Global PPL CH GmbH", "additionalInfoRequired": "Yes", "additionalInfoRule": "Employers can choose not to use per diems and instead reimburse meals against receipts. Cannot mix per diem method with actual expenses on same trip"}, {"travelNonTravelBoth": "Travel", "expenseType": "Per diem payment timing", "icpName": "Global PPL CH GmbH", "additionalInfoRequired": "Yes", "additionalInfoRule": "Per diem can be paid upon the workers return, it does not need to be paid in advance"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Training expenses", "icpName": "Global PPL CH GmbH", "additionalInfoRequired": "Yes", "additionalInfoRule": "Training expenses require the approval of the direct manager"}, {"travelNonTravelBoth": "Both", "expenseType": "Tax exemption proof", "icpName": "Global PPL CH GmbH", "additionalInfoRequired": "Yes", "additionalInfoRule": "Tax exemptions will only be applied providing sufficient proof is shared e.g. tax receipts, invoices etc. Without the correct supporting documents any applicable tax exemption cannot be applied"}]}