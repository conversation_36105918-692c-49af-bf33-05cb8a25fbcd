name: Manual-CICD

on:
  workflow_dispatch:
    inputs:
      environments:
        description: 'Which environments to release to'
        required: true
        type: choice
        options:
          - '["integration", "staging", "customer-sandbox-1", "production"]'
          - '["staging", "customer-sandbox-1", "production"]'
          - '["customer-sandbox-1", "production"]'
          - '["production"]'
      hotfix-tag:
        description: 'Mark if you are releasing a hotfix'
        required: false
        type: boolean

jobs:
  Tests:
    uses: ./.github/workflows/run-tests.yaml
    secrets: inherit
  CICD:
    uses: papayaglobal/workflows/.github/workflows/manual-deploy.yaml@main
    needs: Tests
    with:
      service-name: ${{ github.event.repository.name }}
      node-ver: 20
      docker-context: ./
      dockerfile-path: ./Dockerfile
      runners: payroll
      account: core
      environments: ${{ inputs.environments }}
      hotfix-tag: ${{ inputs.hotfix-tag }}
    secrets: inherit
