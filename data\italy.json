{"receiptStandards": [{"description": "Receipt document type", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "Documents must be actual tax receipts or invoices preferably scanned not photos; booking confirmations will not suffice"}, {"description": "Receipt format quality", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "Online copies are sufficient scanned not photo, a hard copy is not required"}, {"description": "Receipt readability", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "Clear and readable receipts and invoices must be submitted with expenses"}, {"description": "Personal information removal", "travelNonTravelBoth": "Both", "expenseType": "All expenses", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "Any personal information not required for reimbursement purposes should be removed before it is submitted"}, {"description": "Global People company name on invoice", "travelNonTravelBoth": "Non-Travel", "expenseType": "Goods/Services", "icpName": "Global People s.r.l.", "mandatoryOptional": "Mandatory", "rule": "Invoice should show Global People s.r.l."}, {"description": "Global People address on invoice", "travelNonTravelBoth": "Non-Travel", "expenseType": "Goods/Services", "icpName": "Global People s.r.l.", "mandatoryOptional": "Mandatory", "rule": "Via Venti Settembre 3, Torino (TO) CAP 10121, Italy"}, {"description": "Global People VAT on invoice", "travelNonTravelBoth": "Non-Travel", "expenseType": "Goods/Services", "icpName": "Global People s.r.l.", "mandatoryOptional": "Mandatory", "rule": "VAT: *************"}, {"description": "Global People C.F. on invoice", "travelNonTravelBoth": "Non-Travel", "expenseType": "Goods/Services", "icpName": "Global People s.r.l.", "mandatoryOptional": "Mandatory", "rule": "C.F: 12455930011"}, {"description": "GoGlobal company name on invoice", "travelNonTravelBoth": "Non-Travel", "expenseType": "Goods/Services", "icpName": "GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "Invoice should show GoGlobal Consulting S.r.l"}, {"description": "GoGlobal address on invoice", "travelNonTravelBoth": "Non-Travel", "expenseType": "Goods/Services", "icpName": "GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "<PERSON> Modrone 38, 20122 Milano, Italia"}, {"description": "GoGlobal P.IVA on invoice", "travelNonTravelBoth": "Non-Travel", "expenseType": "Goods/Services", "icpName": "GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "P.IVA 12205930964"}, {"description": "Local Employer name requirement", "travelNonTravelBoth": "Non-Travel", "expenseType": "Goods/Services", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "The Local Employers name and details should appear on invoices, not the workers"}, {"description": "Worker name on travel documents", "travelNonTravelBoth": "Travel", "expenseType": "Flight tickets, Hotels", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "Where it is not possible to use the Local Experts name e.g. on flight tickets/hotels, the workers name and details should be used"}, {"description": "End client name prohibition", "travelNonTravelBoth": "Travel", "expenseType": "Flight tickets, Hotels", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "mandatoryOptional": "Mandatory", "rule": "The end client shouldn't be mentioned"}], "compliancePoliciesGrossUpRelated": [{"travelNonTravelBoth": "Both", "expenseType": "All approved expenses", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "Yes", "grossUpRule": "All approved expenses will be paid as NET to the employee and grossed up if they are not tax free"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Business expenses", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "No", "grossUpRule": "Business expenses related to workers completing their job will usually be tax exempt"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Mixed business and personal expenses", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "Yes", "grossUpRule": "Only purely business related elements of this expense will be tax free, anything additional will be subject to tax"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Entertainment expenses (meals with clients)", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "Yes", "grossUpRule": "Tax free up to 75% of the whole amount"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Employee engagement activity", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "No", "grossUpRule": "Tax exempt"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Training and development", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "No", "grossUpRule": "Tax exempt"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Car rental up to 15 days", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "No", "grossUpRule": "Tax exempt"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Car rental longer than 15 days", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "Yes", "grossUpRule": "Will be subjected to taxes"}, {"travelNonTravelBoth": "Travel", "expenseType": "Mileage over 15,000 km annually", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "Yes", "grossUpRule": "Any excess kilometers will be subject to tax"}, {"travelNonTravelBoth": "Travel", "expenseType": "Vehicle expenses (non-assigned car)", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "Yes", "grossUpRule": "Tax exempt up to 20% of their costs as long as the car is not assigned to a specific employee"}, {"travelNonTravelBoth": "Travel", "expenseType": "Transportation expenses with documents", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "No", "grossUpRule": "100% tax exempt if the worker provides evidence of the transportation documents (not applicable for company cars)"}, {"travelNonTravelBoth": "Travel", "expenseType": "Fuel expenses", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "Yes", "grossUpRule": "Fuel expenses are taxed"}, {"travelNonTravelBoth": "Travel", "expenseType": "Trips within municipal area", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "Yes", "grossUpRule": "Subject to a tax exemption of 75% on the per diem rate"}, {"travelNonTravelBoth": "Travel", "expenseType": "Domestic per diem with supporting documents", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "No", "grossUpRule": "Exempt from tax as long as supporting documents such as receipts are provided"}, {"travelNonTravelBoth": "Travel", "expenseType": "International per diem", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "grossUp": "No", "grossUpRule": "Tax free"}], "compliancePoliciesAdditionalInfoRelated": [{"travelNonTravelBoth": "Travel", "expenseType": "Business trips", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "Business Trip - submit a separate report for each trip"}, {"travelNonTravelBoth": "Travel", "expenseType": "Mileage claims", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "Car details (the type of car, whether petrol/electric/hybrid, model) must be listed in the expense report"}, {"travelNonTravelBoth": "Travel", "expenseType": "Mileage claims", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "Information regarding the route and the kilometers traveled, which must be reported with indication of the starting point and arrival point"}, {"travelNonTravelBoth": "Travel", "expenseType": "Mileage claims", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "Scan of the card passport (libretto di circolazione), as it will contain all information regarding the car owner"}, {"travelNonTravelBoth": "Travel", "expenseType": "Mileage claims", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "Any screenshot from any app that can track start point, arrival point and KM will work (like google maps) to keep track of the travelled KM"}, {"travelNonTravelBoth": "Travel", "expenseType": "Mileage claims", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "Full details of the vehicle and proof of distance travelled should be provided as well (e.g. google maps)"}, {"travelNonTravelBoth": "Travel", "expenseType": "Parking fees", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "Parking fees are excluded from the per diem rate and should be reimbursed against receipts"}, {"travelNonTravelBoth": "Travel", "expenseType": "Per diem claims", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "Workers must always provide receipts when per diems are in place"}, {"travelNonTravelBoth": "Travel", "expenseType": "Per diem method selection", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "You cannot mix per diem method with actual expenses. You will need to agree with the worker on one method per business trip"}, {"travelNonTravelBoth": "Travel", "expenseType": "Traceable payment methods", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "Under Budget Law 2025, obligation to use traceable payment methods: Bank transfers, Postal transfers, Credit cards, Debit cards, Prepaid cards, Bank or cashier's checks"}, {"travelNonTravelBoth": "Travel", "expenseType": "Non-traceable payment consequences", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "If reimbursements are not made via traceable methods they will not be considered deductible"}, {"travelNonTravelBoth": "Non-Travel", "expenseType": "Entertainment expenses", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "The worker will always need to mention the 3rd party detail"}, {"travelNonTravelBoth": "Both", "expenseType": "Foreign currency expenses", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "Receipts should be submitted with the same currency and clear exchange rate"}, {"travelNonTravelBoth": "Both", "expenseType": "Tax exemption supporting documents", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "Without supporting documents any applicable tax exemption cannot be applied"}, {"travelNonTravelBoth": "Both", "expenseType": "Sufficient proof requirement", "icpName": "Global People s.r.l. / GoGlobal Consulting S.r.l", "additionalInfoRequired": "Yes", "additionalInfoRule": "Tax exemptions will only be applied providing sufficient proof is shared e.g. tax receipts, invoices etc"}]}