version: '3.8'

services:
  # Redis Service
  redis:
    image: redis:7-alpine
    container_name: expense-processing-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - expense-network

  # Expense Processing Service
  expense-processing-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: expense-processing-app
    ports:
      - "3000:3000"
    depends_on:
      - redis
    environment:
      - NODE_ENV=production
      - PORT=3000
      # Redis Configuration (internal service)
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DB=0
      # AI Service API Keys
      - ANTHROPIC_KEY=${ANTHROPIC_KEY}
      # AWS Configuration
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - LLAMAINDEX_API_KEY=${LLAMAINDEX_API_KEY}
      # AWS Bedrock Configuration
      - BEDROCK_AWS_ACCESS_KEY_ID=${BEDROCK_AWS_ACCESS_KEY_ID}
      - BEDROCK_AWS_SECRET_ACCESS_KEY=${BEDROCK_AWS_SECRET_ACCESS_KEY}
      - BEDROCK_AWS_SESSION_TOKEN=${BEDROCK_AWS_SESSION_TOKEN}
      - BEDROCK_AWS_REGION=${BEDROCK_AWS_REGION}
      - BEDROCK_CLAUDE_MODEL=${BEDROCK_CLAUDE_MODEL}
      # AWS Textract Configuration
      - TEXTRACT_AWS_ACCESS_KEY_ID=${TEXTRACT_AWS_ACCESS_KEY_ID}
      - TEXTRACT_AWS_SECRET_ACCESS_KEY=${TEXTRACT_AWS_SECRET_ACCESS_KEY}
      - TEXTRACT_AWS_REGION=${TEXTRACT_AWS_REGION}
      # Document Reader Configuration
      - DOCUMENT_READER=${DOCUMENT_READER:-llamaparse}
      # File Upload Configuration
      - MAX_FILE_SIZE=${MAX_FILE_SIZE:-50MB}
      - UPLOAD_PATH=/app/uploads
      # Job Queue Configuration
      - QUEUE_CONCURRENCY=${QUEUE_CONCURRENCY:-10}
      - MAX_RETRY_ATTEMPTS=${MAX_RETRY_ATTEMPTS:-3}
      - JOB_TIMEOUT=${JOB_TIMEOUT:-300000}
      # Monitoring
      - ENABLE_SWAGGER=${ENABLE_SWAGGER:-true}
      - ENABLE_THROTTLING=${ENABLE_THROTTLING:-true}
      - THROTTLE_TTL=${THROTTLE_TTL:-60}
      - THROTTLE_LIMIT=${THROTTLE_LIMIT:-100}
    volumes:
      # Persistent storage for uploads and results
      - uploads_data:/app/uploads
      - results_data:/app/results
      - logs_data:/app/logs
      - data_data:/app/data
      - markdown_extractions_data:/app/markdown_extractions
    healthcheck:
      test: ["CMD", "node", "/app/healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    networks:
      - expense-network

volumes:
  redis_data:
    driver: local
  uploads_data:
    driver: local
  results_data:
    driver: local
  logs_data:
    driver: local
  data_data:
    driver: local
  markdown_extractions_data:
    driver: local

networks:
  expense-network:
    driver: bridge
