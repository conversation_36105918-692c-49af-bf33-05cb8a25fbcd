{"image_quality_assessment": {"image_path": "uploads\\v4_run_005_Copy of austrian_file_11.pdf", "assessment_method": "LLM", "model_used": "eu.amazon.nova-pro-v1:0", "timestamp": "2025-07-31T18:09:22.477Z", "quality_score": 100, "quality_level": "excellent", "suitable_for_extraction": true, "blur_detection": {"detected": false, "severity_level": "none", "confidence_score": 0.95, "quantitative_measure": 0, "description": "The text is sharp and clearly defined with no visible blur.", "recommendation": "No action needed."}, "contrast_assessment": {"detected": false, "severity_level": "none", "confidence_score": 0.95, "quantitative_measure": 1, "description": "Excellent contrast between text and background, ensuring clear text recognition.", "recommendation": "No action needed."}, "glare_identification": {"detected": false, "severity_level": "none", "confidence_score": 0.9, "quantitative_measure": 0, "description": "No glare or bright spots detected that obscure text or important document areas.", "recommendation": "No action needed."}, "water_stains": {"detected": false, "severity_level": "none", "confidence_score": 0.9, "quantitative_measure": 0, "description": "No water stains or discolorations affecting document readability.", "recommendation": "No action needed."}, "tears_or_folds": {"detected": false, "severity_level": "none", "confidence_score": 0.9, "quantitative_measure": 0, "description": "No visible tears, creases, folds, or wrinkles that distort text or cause information loss.", "recommendation": "No action needed."}, "cut_off_detection": {"detected": false, "severity_level": "none", "confidence_score": 0.95, "quantitative_measure": 0, "description": "No document edges are cut off, and the image frame includes all important portions.", "recommendation": "No action needed."}, "missing_sections": {"detected": false, "severity_level": "none", "confidence_score": 0.9, "quantitative_measure": 0, "description": "No missing or incomplete sections; the entire receipt/invoice is captured.", "recommendation": "No action needed."}, "obstructions": {"detected": false, "severity_level": "none", "confidence_score": 0.9, "quantitative_measure": 0, "description": "No objects, fingers, shadows, or other elements block or obscure document content.", "recommendation": "No action needed."}, "overall_quality_score": 10}, "classification": {"is_expense": true, "expense_type": "accommodation", "language": "German", "language_confidence": 95, "document_location": "Austria", "expected_location": "Austria", "location_match": true, "error_type": null, "error_message": null, "classification_confidence": 95, "reasoning": "The document contains multiple fields indicating it is an expense document. It includes details such as the supplier, consumer, transaction amount, transaction date, invoice number, tax information, payment method, and item description line items.", "schema_field_analysis": {"fields_found": ["supplier", "consumerRecipient", "transactionAmount", "transactionDate", "invoiceReceiptNumber", "taxInformation", "paymentMethod", "itemDescriptionLineItems"], "fields_missing": [], "total_fields_found": 8, "expense_identification_reasoning": "All 8 required fields are present, confirming this is an expense document."}}, "extraction": {"customer_name_on_invoice": "<PERSON>", "customer_address_on_invoice": "<PERSON> Straße 21, 2460 Bruck an der Leiha", "customer_vat_number_on_invoice": null, "currency": "EUR", "amount": 28, "receipt_type": "<PERSON><PERSON><PERSON><PERSON>", "receipt_quality": null, "personal_information": null, "business_trip_reporting": null, "travel_template": null, "manager_approval": null, "route_map": null, "kilometer_record": null, "car_details": null, "parking_documentation": null, "line_items": [{"description": "<PERSON><PERSON> #223", "quantity": 3, "unit_price": 160.12, "total_price": 480.36}, {"description": "Businesspackage B Schober Tamara #223", "quantity": 3, "unit_price": 1.5, "total_price": 4.5}, {"description": "Restaurant lt. Beleg Nr.: 22786 Schober Tamara #223", "quantity": 1, "unit_price": 28, "total_price": 28}, {"description": "Deposit 7.0% Schober Tamara #223", "quantity": 1, "unit_price": -484.86, "total_price": -484.86}, {"description": "American Express Schober #223", "quantity": 1, "unit_price": -28, "total_price": 0}], "total": 28, "payment": -28, "outstanding_amount": 0, "tax_details": [{"tax_rate": 7, "net_amount": 448.93, "tax_amount": 31.43, "gross_amount": 480.36}, {"tax_rate": 19, "net_amount": 27.31, "tax_amount": 5.19, "gross_amount": 32.5}, {"tax_rate": 0, "net_amount": 0, "tax_amount": 0, "gross_amount": 0}], "invoice_number": "41464", "invoice_date": "2024-12-13", "arrival_date": "2024-12-10", "departure_date": "2024-12-13", "guest_name": "<PERSON><PERSON><PERSON>", "room_number": "223", "supplier_name": "SLY", "supplier_address": "Petersburger Str. 16, 10249 Berlin", "bank_details": "<PERSON><PERSON> Sparkasse, Simon<PERSON>von-<PERSON> Str. 33, 20359 Hamburg, IBAN: **********************, BIC: HASPADEHHXXX", "contact_person": "<PERSON><PERSON>, <PERSON>, <PERSON>", "contact_phone": "+*************", "contact_email": "<EMAIL>", "contact_website": "www.sly-berlin.com", "vat_id": "DE321074931", "tse_transaction": "120941", "tse_signature_counter": "242401", "tse_start": "2024-12-13T10:47:31+01:00", "tse_stop": "2024-12-13T10:47:31+01:00", "tse_serial_number": "0069f29c-4c64-4db9-832a-0f3b92dfdfee", "tse_time_format": "unixTime", "tse_hash_algorithm": "ecdsa-plain-SHA256", "cash_register_id": "protel-0TZdN3UL8nEaPnGGaDbLUhg", "initial_order": "2024-12-13 09:47:31", "tse_signature": "LdGvOSf11T9tJP1diG7h9befTPtlKsnrUYZ7Rb0evInfh2FEvWphilVuPxvKxSI49KqxJxvzFaolmybvlG8A==", "tse_public_key": "BPHdQwj1PXdHdMyBIc8Od/hrM75qi4MoVj1dFDO8pY5x56Bm3uiujR31pepRv42t75oGMgEcPD5kpw5Ib0HMoD0="}, "compliance": {"validation_result": {"is_valid": false, "issues_count": 4, "issues": [{"issue_type": "Standards & Compliance | Fix Identified", "field": "customer_name_on_invoice", "description": "The customer name on the invoice is '<PERSON> Schober', but it should be 'Global People IT-Services GmbH' as per the ICP-specific requirements.", "recommendation": "It is recommended to address this issue with the supplier or provider to ensure the correct customer name is displayed on the invoice.", "knowledge_base_reference": "Must show Global People IT-Services GmbH as customer"}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "customer_address_on_invoice", "description": "The customer address on the invoice is 'Robert <PERSON>erer Straße 21, 2460 Bruck an der Leiha', but it should be 'Kärntner Ring 12, A-1010 Vienna, Austria' as per the ICP-specific requirements.", "recommendation": "It is recommended to address this issue with the supplier or provider to ensure the correct customer address is displayed on the invoice.", "knowledge_base_reference": "Must show Kärntner Ring 12, A-1010 Vienna, Austria"}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "customer_vat_number_on_invoice", "description": "The customer VAT number on the invoice is missing, but it should be 'ATU77112189' as per the ICP-specific requirements.", "recommendation": "It is recommended to address this issue with the supplier or provider to ensure the correct customer VAT number is displayed on the invoice.", "knowledge_base_reference": "Must show ATU77112189"}, {"issue_type": "Standards & Compliance | Follow-up Action Identified", "field": "receipt_quality", "description": "The receipt quality field is null, and it is mandatory to ensure the document quality meets the standard.", "recommendation": "Please ensure that the receipt is clear and readable as per the compliance requirements.", "knowledge_base_reference": "Online copies sufficient, hard copy not required"}], "corrected_receipt": null, "compliance_summary": "The receipt has several compliance issues related to the customer name, address, and VAT number. Additionally, the receipt quality needs to be verified. These issues need to be addressed with the supplier to ensure compliance with the ICP-specific requirements."}, "technical_details": {"content_type": "expense_receipt", "country": "Austria", "icp": "Global People", "receipt_type": "accommodation", "issues_count": 4}}, "citations": {"citations": {"customer_name_on_invoice": {"field_citation": {"source_text": "Customer Name on Invoice", "confidence": 1, "source_location": "requirements", "context": "Local Employer name as customer on supplier invoice", "match_type": "exact"}, "value_citation": {"source_text": "<PERSON>", "confidence": 1, "source_location": "markdown", "context": "SLY\nFrau\nTamara Schober\nRobert <PERSON> Straße 21\n2460 Bruck an der Leiha", "match_type": "exact"}}, "customer_address_on_invoice": {"field_citation": {"source_text": "Customer Address on Invoice", "confidence": 1, "source_location": "requirements", "context": "Local Employer address as customer on supplier invoice", "match_type": "exact"}, "value_citation": {"source_text": "<PERSON> Straße 21, 2460 Bruck an der Leiha", "confidence": 1, "source_location": "markdown", "context": "SLY\nFrau\nTamara Schober\nRobert <PERSON> Straße 21\n2460 Bruck an der Leiha", "match_type": "exact"}}, "currency": {"field_citation": {"source_text": "<PERSON><PERSON><PERSON><PERSON>", "confidence": 1, "source_location": "requirements", "context": "Receipt currency and exchange rate", "match_type": "exact"}, "value_citation": {"source_text": "EUR", "confidence": 1, "source_location": "markdown", "context": "Einzelpreis\nSumme\n160,12\n480,36\n1,50\n4,50\n28,00\n28,00\n-484,86\n-484,86\n-28,00\n0,00", "match_type": "exact"}}, "amount": {"field_citation": {"source_text": "Amount", "confidence": 1, "source_location": "requirements", "context": "Expense amount", "match_type": "exact"}, "value_citation": {"source_text": "28", "confidence": 1, "source_location": "markdown", "context": "Total:\n28,00", "match_type": "exact"}}, "receipt_type": {"field_citation": {"source_text": "Receipt Type", "confidence": 1, "source_location": "requirements", "context": "Type of supporting document", "match_type": "exact"}, "value_citation": {"source_text": "<PERSON><PERSON><PERSON><PERSON>", "confidence": 1, "source_location": "markdown", "context": "Rechnung Nr. 41464/Seite 1/Datum: 13.12.24", "match_type": "exact"}}, "line_items": {"field_citation": {"source_text": "Line Items", "confidence": 1, "source_location": "requirements", "context": "Detailed breakdown of expenses", "match_type": "exact"}, "value_citation": {"source_text": "<PERSON><PERSON> #223, 3, 160.12, 480.36", "confidence": 1, "source_location": "markdown", "context": "Menge\nLeistung\nEinzelpreis\nSumme\n3\nLogis Schober Tamara #223\n160,12\n480,36", "match_type": "exact"}}, "total": {"field_citation": {"source_text": "Total", "confidence": 1, "source_location": "requirements", "context": "Sum of all line items", "match_type": "exact"}, "value_citation": {"source_text": "28", "confidence": 1, "source_location": "markdown", "context": "Total:\n28,00", "match_type": "exact"}}, "payment": {"field_citation": {"source_text": "Payment", "confidence": 1, "source_location": "requirements", "context": "Amount paid", "match_type": "exact"}, "value_citation": {"source_text": "-28", "confidence": 1, "source_location": "markdown", "context": "Zahlung:\n-28,00", "match_type": "exact"}}, "outstanding_amount": {"field_citation": {"source_text": "Outstanding Amount", "confidence": 1, "source_location": "requirements", "context": "Remaining amount due", "match_type": "exact"}, "value_citation": {"source_text": "0", "confidence": 1, "source_location": "markdown", "context": "Offener Betrag:\n0,00", "match_type": "exact"}}, "tax_details": {"field_citation": {"source_text": "Tax Details", "confidence": 1, "source_location": "requirements", "context": "Detailed tax information", "match_type": "exact"}, "value_citation": {"source_text": "7, 448.93, 31.43, 480.36", "confidence": 1, "source_location": "markdown", "context": "Enthaltene Mehrwertsteuer:\nMwSt-Satz\nNettobetrag EUR\nMehrwertsteuer EUR\nBruttobetrag EUR\n7,00% A\n448,93\n31,43\n480,36", "match_type": "exact"}}, "invoice_number": {"field_citation": {"source_text": "Invoice Number", "confidence": 1, "source_location": "requirements", "context": "Unique identifier for the invoice", "match_type": "exact"}, "value_citation": {"source_text": "41464", "confidence": 1, "source_location": "markdown", "context": "Rechnung Nr. 41464/Seite 1/Datum: 13.12.24", "match_type": "exact"}}, "invoice_date": {"field_citation": {"source_text": "Invoice Date", "confidence": 1, "source_location": "requirements", "context": "Date of the invoice", "match_type": "exact"}, "value_citation": {"source_text": "2024-12-13", "confidence": 1, "source_location": "markdown", "context": "Rechnung Nr. 41464/Seite 1/Datum: 13.12.24", "match_type": "fuzzy"}}, "arrival_date": {"field_citation": {"source_text": "Arrival Date", "confidence": 1, "source_location": "requirements", "context": "Date of arrival", "match_type": "exact"}, "value_citation": {"source_text": "2024-12-10", "confidence": 1, "source_location": "markdown", "context": "Anreise: 10.12.24 <PERSON><PERSON><PERSON>: 13.12.24 Gast: Sc<PERSON>ber", "match_type": "fuzzy"}}, "departure_date": {"field_citation": {"source_text": "Departure Date", "confidence": 1, "source_location": "requirements", "context": "Date of departure", "match_type": "exact"}, "value_citation": {"source_text": "2024-12-13", "confidence": 1, "source_location": "markdown", "context": "Anreise: 10.12.24 <PERSON><PERSON><PERSON>: 13.12.24 Gast: Sc<PERSON>ber", "match_type": "fuzzy"}}, "guest_name": {"field_citation": {"source_text": "Guest Name", "confidence": 1, "source_location": "requirements", "context": "Name of the guest", "match_type": "exact"}, "value_citation": {"source_text": "<PERSON><PERSON><PERSON>", "confidence": 1, "source_location": "markdown", "context": "Anreise: 10.12.24 <PERSON><PERSON><PERSON>: 13.12.24 Gast: Sc<PERSON>ber", "match_type": "exact"}}, "room_number": {"field_citation": {"source_text": "Room Number", "confidence": 1, "source_location": "requirements", "context": "Room number of the guest", "match_type": "exact"}, "value_citation": {"source_text": "223", "confidence": 1, "source_location": "markdown", "context": "Zimmer: 223", "match_type": "exact"}}, "supplier_name": {"field_citation": {"source_text": "Supplier Name", "confidence": 1, "source_location": "requirements", "context": "Name of the supplier", "match_type": "exact"}, "value_citation": {"source_text": "SLY", "confidence": 1, "source_location": "markdown", "context": "SLY", "match_type": "exact"}}, "supplier_address": {"field_citation": {"source_text": "Supplier Address", "confidence": 1, "source_location": "requirements", "context": "Address of the supplier", "match_type": "exact"}, "value_citation": {"source_text": "Petersburger Str. 16, 10249 Berlin", "confidence": 1, "source_location": "markdown", "context": "RECHNUNGSANSCHRIFT\nPetersburger Str. 16\n10249 Berlin", "match_type": "exact"}}, "bank_details": {"field_citation": {"source_text": "Bank Details", "confidence": 1, "source_location": "requirements", "context": "Bank details of the supplier", "match_type": "exact"}, "value_citation": {"source_text": "<PERSON><PERSON> Sparkasse, Simon<PERSON>von-<PERSON> Str. 33, 20359 Hamburg, IBAN: **********************, BIC: HASPADEHHXXX", "confidence": 1, "source_location": "markdown", "context": "BANKVERBINDUNG\nHamburger Sparkasse\nSimon-von-Utrecht Str. 33\n20359 Hamburg\n**********************\nHASPADEHHXXX", "match_type": "exact"}}, "contact_person": {"field_citation": {"source_text": "Contact Person", "confidence": 1, "source_location": "requirements", "context": "Contact person for the supplier", "match_type": "exact"}, "value_citation": {"source_text": "<PERSON><PERSON>, <PERSON>, <PERSON>", "confidence": 1, "source_location": "markdown", "context": "GESCHÄFTSFÜHRUNG\nIn<PERSON> K<PERSON>e\nThomas K<PERSON>e\n<PERSON>", "match_type": "exact"}}, "contact_phone": {"field_citation": {"source_text": "Contact Phone", "confidence": 1, "source_location": "requirements", "context": "Contact phone number for the supplier", "match_type": "exact"}, "value_citation": {"source_text": "+*************", "confidence": 1, "source_location": "markdown", "context": "P: +*************", "match_type": "exact"}}, "contact_email": {"field_citation": {"source_text": "Contact Email", "confidence": 1, "source_location": "requirements", "context": "Contact email for the supplier", "match_type": "exact"}, "value_citation": {"source_text": "<EMAIL>", "confidence": 1, "source_location": "markdown", "context": "<EMAIL>", "match_type": "exact"}}, "contact_website": {"field_citation": {"source_text": "Contact Website", "confidence": 1, "source_location": "requirements", "context": "Contact website for the supplier", "match_type": "exact"}, "value_citation": {"source_text": "www.sly-berlin.com", "confidence": 1, "source_location": "markdown", "context": "www.sly-berlin.com", "match_type": "exact"}}, "vat_id": {"field_citation": {"source_text": "VAT ID", "confidence": 1, "source_location": "requirements", "context": "VAT identification number for the supplier", "match_type": "exact"}, "value_citation": {"source_text": "DE321074931", "confidence": 1, "source_location": "markdown", "context": "USt. ID: DE321074931", "match_type": "exact"}}}, "metadata": {"total_fields_analyzed": 25, "fields_with_field_citations": 25, "fields_with_value_citations": 25, "average_confidence": 1}}, "timing": {"phase_timings": {"markdown_extraction_seconds": "10.5", "file_classification_seconds": "3.1", "image_quality_assessment_seconds": "5.3", "data_extraction_seconds": "27.1", "issue_detection_seconds": "14.1", "citation_generation_seconds": "28.0"}, "agent_performance": {"markdown_extraction": {"start_time": "2025-07-31T18:09:06.689Z", "end_time": "2025-07-31T18:09:17.197Z", "duration_seconds": "10.5", "document_reader_used": "textract"}, "file_classification": {"start_time": "2025-07-31T18:09:17.200Z", "end_time": "2025-07-31T18:09:20.329Z", "duration_seconds": "3.1", "model_used": "eu.amazon.nova-pro-v1:0", "execution_mode": "parallel"}, "image_quality_assessment": {"start_time": "2025-07-31T18:09:17.198Z", "end_time": "2025-07-31T18:09:22.477Z", "duration_seconds": "5.3", "model_used": "eu.amazon.nova-pro-v1:0", "execution_mode": "parallel"}, "data_extraction": {"start_time": "2025-07-31T18:09:17.201Z", "end_time": "2025-07-31T18:09:44.320Z", "duration_seconds": "27.1", "model_used": "eu.amazon.nova-pro-v1:0", "execution_mode": "parallel"}, "issue_detection": {"start_time": "2025-07-31T18:09:44.321Z", "end_time": "2025-07-31T18:09:58.440Z", "duration_seconds": "14.1", "model_used": "eu.amazon.nova-pro-v1:0", "execution_mode": "parallel"}, "citation_generation": {"start_time": "2025-07-31T18:09:44.323Z", "end_time": "2025-07-31T18:10:12.280Z", "duration_seconds": "28.0", "model_used": "eu.amazon.nova-pro-v1:0", "execution_mode": "parallel"}}, "total_processing_time_seconds": "65.6", "performance_metrics": {"parallel_group_1_seconds": "27.1", "parallel_group_2_seconds": "28.0", "total_parallel_time_seconds": "55.1", "estimated_sequential_time_seconds": "88.1", "estimated_speedup_factor": "1.60"}, "validation": {"total_time_seconds": "65.6", "expected_parallel_time_seconds": "65.6", "sequential_sum_seconds": "88.1", "difference_seconds": "0.0", "is_consistent": true, "tolerance_seconds": "3.0", "processing_mode": "parallel", "time_saved_seconds": "22.5"}}, "metadata": {"filename": "Copy of austrian_file_11.pdf", "processing_time": 65591, "country": "Austria", "icp": "Global People", "processed_at": "2025-07-31T18:10:12.281Z", "optimization": {"parallel_processing": true, "parallel_group_1_duration_seconds": "27.1", "parallel_group_2_duration_seconds": "28.0", "estimated_sequential_time_seconds": "88.1", "actual_parallel_time_seconds": "65.6"}}}